{"app": {"title": "Claude <PERSON> Router", "save": "Save", "save_and_restart": "Save and Restart", "cancel": "Cancel", "edit": "Edit", "remove": "Remove", "delete": "Delete", "settings": "Settings", "selectFile": "Select File", "config_saved_success": "Config saved successfully", "config_saved_failed": "Failed to save config", "config_saved_restart_success": "Config saved and service restarted successfully", "config_saved_restart_failed": "Failed to save config and restart service"}, "login": {"title": "Sign in to your account", "description": "Enter your API key to access the configuration panel", "apiKey": "API Key", "apiKeyPlaceholder": "Enter your API key", "signIn": "Sign In", "invalidApiKey": "Invalid API key", "configError": "Configuration not loaded", "validating": "Validating API key..."}, "toplevel": {"title": "General Settings", "log": "Enable Logging", "claude_path": "<PERSON>", "host": "Host", "port": "Port", "apikey": "API Key", "timeout": "API Timeout (ms)", "proxy_url": "Proxy URL"}, "transformers": {"title": "Custom Transformers", "path": "Path", "project": "Project", "remove": "Remove", "add": "Add Custom Transformer", "edit": "Edit Custom Transformer", "delete": "Delete Custom Transformer", "delete_transformer_confirm": "Are you sure you want to delete this custom transformer?", "parameters": "Parameters"}, "providers": {"title": "Providers", "name": "Name", "api_base_url": "API Full URL", "api_key": "API Key", "models": "Models", "models_placeholder": "Enter model name and press Enter to add", "add_model": "Add Model", "select_models": "Select Models", "remove": "Remove", "add": "Add Provider", "edit": "Edit Provider", "delete": "Delete", "cancel": "Cancel", "delete_provider_confirm": "Are you sure you want to delete this provider?", "test_connectivity": "Test Connectivity", "testing": "Testing...", "connection_successful": "Connection successful!", "connection_failed": "Connection failed!", "missing_credentials": "Missing API base URL or API key", "fetch_available_models": "Fetch available models", "fetching_models": "Fetching models...", "fetch_models_failed": "Failed to fetch models", "transformers": "Transformers", "select_transformer": "Select Transformer", "no_transformers": "No transformers available", "provider_transformer": "Provider Transformer", "model_transformers": "Model Transformers", "transformer_parameters": "Transformer Parameters", "add_parameter": "Add Parameter", "parameter_name": "Parameter Name", "parameter_value": "Parameter Value", "selected_transformers": "Selected Transformers", "import_from_template": "Import from template", "no_templates_found": "No templates found", "select_template": "Select a template...", "api_key_required": "API Key is required", "name_required": "Name is required", "name_duplicate": "A provider with this name already exists"}, "router": {"title": "Router", "default": "<PERSON><PERSON><PERSON>", "background": "Background", "think": "Think", "longContext": "Long Context", "longContextThreshold": "Context Threshold", "webSearch": "Web Search", "selectModel": "Select a model...", "searchModel": "Search model...", "noModelFound": "No model found."}, "json_editor": {"title": "JSON Editor", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "save_failed": "Failed to save config", "save_and_restart": "Save & Restart"}}